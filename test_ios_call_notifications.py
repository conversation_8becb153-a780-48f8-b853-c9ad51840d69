#!/usr/bin/env python3
"""
Test script to verify iOS-specific call notifications are working correctly.
This script tests the vm_request functionality with iOS device_os settings.
"""

import asyncio
import json
import sys
import os
from unittest.mock import Mock, AsyncMock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from apps.actions_v3 import ActionHandlerV3
from models import User, Consultant
from models.calls import Call
from utils.fcm_notification import send_notification


class TestIOSCallNotifications:
    def __init__(self):
        self.handler = ActionHandlerV3()
        
    def setup_mock_objects(self):
        """Setup mock objects for testing"""
        # Mock database session
        self.mock_db = Mock()
        self.mock_session = Mock()
        self.mock_db.session = self.mock_session
        
        # Mock user (client)
        self.mock_user = Mock(spec=User)
        self.mock_user.id = 1
        self.mock_user.fullname = "Test User"
        self.mock_user.avatar_url = "https://example.com/avatar.jpg"
        self.mock_user.username = "testuser"
        
        # Mock consultant with iOS device
        self.mock_consultant_ios = Mock(spec=Consultant)
        self.mock_consultant_ios.id = 1
        self.mock_consultant_ios.username = "consultant_ios"
        self.mock_consultant_ios.device_os = "ios"  # iOS device
        self.mock_consultant_ios.fcm = "ios_fcm_token_123"
        self.mock_consultant_ios.status = "online"
        self.mock_consultant_ios.video_call_cost = 10
        self.mock_consultant_ios.voice_call_cost = 5
        self.mock_consultant_ios.session_duration = 30
        
        # Mock consultant with Android device
        self.mock_consultant_android = Mock(spec=Consultant)
        self.mock_consultant_android.id = 2
        self.mock_consultant_android.username = "consultant_android"
        self.mock_consultant_android.device_os = "android"  # Android device
        self.mock_consultant_android.fcm = "android_fcm_token_456"
        self.mock_consultant_android.status = "online"
        self.mock_consultant_android.video_call_cost = 10
        self.mock_consultant_android.voice_call_cost = 5
        self.mock_consultant_android.session_duration = 30
        
        # Mock call object
        self.mock_call = Mock(spec=Call)
        self.mock_call.id = 1
        self.mock_call.call_type = "video"
        self.mock_call.status = "unconfirmed"
        self.mock_call.client = self.mock_user
        
        # Mock WebSocket connection
        self.mock_connection = AsyncMock()
        
        # Setup handler attributes
        self.handler.db = self.mock_db
        self.handler.from_user = self.mock_user
        self.handler.connection = self.mock_connection
        
    async def test_ios_notification_format(self):
        """Test that iOS notifications are formatted correctly"""
        print("Testing iOS notification format...")
        
        self.setup_mock_objects()
        
        # Setup database query mocks
        self.mock_session.query.return_value.filter.return_value.first.return_value = self.mock_consultant_ios
        self.mock_call.consultant = self.mock_consultant_ios
        self.mock_session.refresh = Mock()
        
        # Mock the call creation
        def mock_add_call(call):
            call.id = 1
            call.consultant = self.mock_consultant_ios
            call.client = self.mock_user
            
        self.mock_session.add.side_effect = mock_add_call
        
        # Test data for video call request
        test_data = {
            'action': 'vmRequest',
            'consultant': 'consultant_ios',
            'chat_type': 'video'
        }
        
        # Mock send_notification to capture the call
        with patch('apps.actions_v3.send_notification') as mock_send_notification:
            mock_send_notification.return_value = AsyncMock()
            
            # Call the vm_request method
            result = await self.handler.vm_request(test_data)
            
            # Verify send_notification was called
            assert mock_send_notification.called, "send_notification should have been called"
            
            # Get the call arguments
            call_args = mock_send_notification.call_args
            args, kwargs = call_args
            
            # Verify the notification parameters
            assert kwargs['platform'] == 'ios', f"Expected platform 'ios', got '{kwargs['platform']}'"
            assert kwargs['ids'] == ['ios_fcm_token_123'], f"Expected iOS FCM token, got {kwargs['ids']}"
            assert kwargs['title'] == "Incoming Call", f"Expected 'Incoming Call' title, got '{kwargs['title']}'"
            assert "Test User is calling you" in kwargs['body'], f"Expected user name in body, got '{kwargs['body']}'"
            
            # Verify notification data contains required fields
            notification_data = kwargs['data']
            assert notification_data['notification_type'] == 'incoming_call'
            assert notification_data['call_type'] == 'video'
            assert notification_data['from_user_fullname'] == 'Test User'
            
            print("✅ iOS notification format test passed!")
            return True
            
    async def test_android_notification_format(self):
        """Test that Android notifications maintain backward compatibility"""
        print("Testing Android notification format...")
        
        self.setup_mock_objects()
        
        # Setup database query mocks for Android consultant
        self.mock_session.query.return_value.filter.return_value.first.return_value = self.mock_consultant_android
        self.mock_call.consultant = self.mock_consultant_android
        
        # Mock the call creation
        def mock_add_call(call):
            call.id = 1
            call.consultant = self.mock_consultant_android
            call.client = self.mock_user
            
        self.mock_session.add.side_effect = mock_add_call
        
        # Test data for voice call request
        test_data = {
            'action': 'vmRequest',
            'consultant': 'consultant_android',
            'chat_type': 'voice'
        }
        
        # Mock send_notification to capture the call
        with patch('apps.actions_v3.send_notification') as mock_send_notification:
            mock_send_notification.return_value = AsyncMock()
            
            # Call the vm_request method
            result = await self.handler.vm_request(test_data)
            
            # Verify send_notification was called
            assert mock_send_notification.called, "send_notification should have been called"
            
            # Get the call arguments
            call_args = mock_send_notification.call_args
            args, kwargs = call_args
            
            # Verify the notification parameters
            assert kwargs['platform'] == 'android', f"Expected platform 'android', got '{kwargs['platform']}'"
            assert kwargs['ids'] == ['android_fcm_token_456'], f"Expected Android FCM token, got {kwargs['ids']}"
            
            print("✅ Android notification format test passed!")
            return True
            
    async def test_missing_device_os_fallback(self):
        """Test fallback to Android when device_os is not set"""
        print("Testing device_os fallback...")
        
        self.setup_mock_objects()
        
        # Create consultant without device_os
        mock_consultant_no_os = Mock(spec=Consultant)
        mock_consultant_no_os.id = 3
        mock_consultant_no_os.username = "consultant_no_os"
        mock_consultant_no_os.device_os = None  # No device_os set
        mock_consultant_no_os.fcm = "no_os_fcm_token_789"
        mock_consultant_no_os.status = "online"
        mock_consultant_no_os.video_call_cost = 10
        mock_consultant_no_os.voice_call_cost = 5
        mock_consultant_no_os.session_duration = 30
        
        # Setup database query mocks
        self.mock_session.query.return_value.filter.return_value.first.return_value = mock_consultant_no_os
        
        # Mock the call creation
        def mock_add_call(call):
            call.id = 1
            call.consultant = mock_consultant_no_os
            call.client = self.mock_user
            
        self.mock_session.add.side_effect = mock_add_call
        
        # Test data
        test_data = {
            'action': 'vmRequest',
            'consultant': 'consultant_no_os',
            'chat_type': 'video'
        }
        
        # Mock send_notification to capture the call
        with patch('apps.actions_v3.send_notification') as mock_send_notification:
            mock_send_notification.return_value = AsyncMock()
            
            # Call the vm_request method
            result = await self.handler.vm_request(test_data)
            
            # Verify send_notification was called
            assert mock_send_notification.called, "send_notification should have been called"
            
            # Get the call arguments
            call_args = mock_send_notification.call_args
            args, kwargs = call_args
            
            # Verify fallback to Android
            assert kwargs['platform'] == 'android', f"Expected fallback to 'android', got '{kwargs['platform']}'"
            
            print("✅ Device OS fallback test passed!")
            return True

    async def run_all_tests(self):
        """Run all tests"""
        print("🧪 Starting iOS Call Notification Tests...\n")
        
        try:
            await self.test_ios_notification_format()
            await self.test_android_notification_format()
            await self.test_missing_device_os_fallback()
            
            print("\n🎉 All tests passed! iOS call notifications are working correctly.")
            return True
            
        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return False


async def main():
    """Main test function"""
    tester = TestIOSCallNotifications()
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ iOS call notification implementation is ready!")
        print("📱 iOS devices will now receive properly formatted call notifications")
        print("🤖 Android devices maintain backward compatibility")
    else:
        print("\n❌ Tests failed. Please check the implementation.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
